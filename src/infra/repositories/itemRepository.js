const BaseRepository = require('./baseRepository');
const historyRepository = require('./historyRepository');

class ItemRepository extends BaseRepository {
  constructor({ historyRepository, db, clientErrors, fastify, constants }) {
    super(db.itemModel);
    this.constants = constants;
    this.historyRepository = historyRepository;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.db = db;
  }

  async bulkCreate(payload) {
    const payloadIds = payload.map((item) => item.acctCd);

    const existingRecords = await this.tableName.findAll({
      where: {
        itemCd: payloadIds,
      },
      attributes: ['itemCd'],
    });

    const existingItemCds = new Set(
      existingRecords.map((record) => record.itemCd),
    );

    const result = await this.tableName.bulkCreate(payload, {
      updateOnDuplicate: [
        'itemCd',
        'itmDes',
        'acctCd',
        'gfq',
        'remainingGfq',
        'tradeCode',
      ],
      returning: true,
    });

    const newlyCreatedRecords = result.filter(
      (record) => !existingItemCds.has(record.itemCd),
    );

    return newlyCreatedRecords;
  }

  async getAllItems(payload) {
    const {
      limit = 10,
      page = 1,
      order = [['itmDes', 'ASC']],
      whereClause,
      paginate,
      searchBy,
      filterBy,
    } = payload;

    // Build WHERE conditions
    let whereConditions = [];
    let replacements = {};

    // Handle legacy whereClause parameter
    if (whereClause) {
      Object.entries(whereClause).forEach(([key, value]) => {
        const dbField = this.#mapFieldToColumn(key);
        if (typeof value === 'string') {
          whereConditions.push(`${dbField} = :${key}`);
          replacements[key] = value;
        } else if (value && value[this.Sequelize.Op.iLike]) {
          whereConditions.push(`${dbField} ILIKE :${key}`);
          replacements[key] = value[this.Sequelize.Op.iLike];
        } else if (value && value[this.Sequelize.Op.eq]) {
          whereConditions.push(`${dbField} = :${key}`);
          replacements[key] = value[this.Sequelize.Op.eq];
        }
      });
    }

    // Handle filterBy (global search)
    if (filterBy) {
      if (typeof filterBy === 'string') {
        const searchTerm = filterBy.trim();
        if (searchTerm) {
          whereConditions.push(`(
            i.itm_des ILIKE :searchTerm OR 
            i.item_cd ILIKE :searchTerm OR 
            i.acct_cd ILIKE :searchTerm
          )`);
          replacements.searchTerm = `%${searchTerm}%`;
        }
      } else if (typeof filterBy === 'object') {
        // Handle object filterBy
        Object.entries(filterBy).forEach(([key, value]) => {
          const dbField = this.#mapFieldToColumn(key);
          if (key === 'itmDes') {
            whereConditions.push(`${dbField} ILIKE :filter_${key}`);
            replacements[`filter_${key}`] = `%${value}%`;
          } else if (key === 'itemCd') {
            whereConditions.push(`${dbField} = :filter_${key}`);
            replacements[`filter_${key}`] = value;
          } else if (key === 'acctCd') {
            whereConditions.push(`${dbField} = :filter_${key}`);
            replacements[`filter_${key}`] = value;
          } else if (key === 'unit') {
            whereConditions.push(`${dbField} = :filter_${key}`);
            replacements[`filter_${key}`] = value;
          } else if (key === 'trade') {
            whereConditions.push(`t.trade_name ILIKE :filter_${key}`);
            replacements[`filter_${key}`] = `%${value}%`;
          }
        });
      }
    }

    // Handle searchBy conditions
    if (searchBy && typeof searchBy === 'object') {
      Object.entries(searchBy).forEach(([key, value]) => {
        const dbField = this.#mapFieldToColumn(key);
        if (key === 'itmDes') {
          whereConditions.push(`${dbField} ILIKE :search_${key}`);
          replacements[`search_${key}`] = `%${value}%`;
        } else if (key === 'itemCd') {
          whereConditions.push(`${dbField} ILIKE :search_${key}`);
          replacements[`search_${key}`] = `%${value}%`;
        } else if (key === 'acctCd') {
          whereConditions.push(`${dbField} ILIKE :search_${key}`);
          replacements[`search_${key}`] = `%${value}%`;
        } else if (key === 'unit') {
          whereConditions.push(`${dbField} ILIKE :search_${key}`);
          replacements[`search_${key}`] = `%${value}%`;
        } else if (key === 'trade') {
          whereConditions.push(`t.trade_name ILIKE :search_${key}`);
          replacements[`search_${key}`] = `%${value}%`;
        }
      });
    }

    // Build ORDER BY clause
    let orderClause = 'ORDER BY i.itm_des ASC';
    if (order && order.length > 0) {
      const fieldMapping = {
        itmDes: 'i.itm_des',
        itemCd: 'i.item_cd',
        unit: 'i.unit',
        acctCd: 'i.acct_cd',
        remainingGfq: 'i.remaining_gfq',
        isSteelbars: 'i.is_steelbars',
        tradeName: 't.trade_name',
        createdAt: 'i.created_at',
        updatedAt: 'i.updated_at',
      };

      const orderParts = order.map(([field, direction]) => {
        const mappedField = fieldMapping[field] || `i.${field}`;
        return `${mappedField} ${direction.toUpperCase()}`;
      });

      if (orderParts.length > 0) {
        orderClause = `ORDER BY ${orderParts.join(', ')}`;
      }
    }

    // Build final query
    const whereClauseStr = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}` 
      : '';

    // Count query
    const countQuery = `
      SELECT COUNT(DISTINCT i.id) as count
      FROM items i
      INNER JOIN trades t ON i.trade_code = t.trade_code
      ${whereClauseStr}
    `;

    // Data query
    const dataQuery = `
      SELECT DISTINCT
        i.id,
        i.item_cd as "itemCd",
        i.itm_des as "itmDes",
        i.unit,
        i.acct_cd as "acctCd",
        i.gfq,
        i.remaining_gfq as "remainingGfq",
        i.is_steelbars as "isSteelbars",
        i.trade_code as "tradeCode",
        i.created_at as "createdAt",
        i.updated_at as "updatedAt",
        t.trade_code as "trade.code",
        t.trade_name as "trade.tradeName",
        s.id as "steelbars.id",
        s.weight as "steelbars.weight",
        s.ofm_acctcd as "steelbars.ofmAcctcd"
      FROM items i
      INNER JOIN trades t ON i.trade_code = t.trade_code
      LEFT JOIN steelbars s ON s.ofm_acctcd = i.acct_cd
      ${whereClauseStr}
      ${orderClause}
      ${paginate ? `LIMIT :limit OFFSET :offset` : ''}
    `;

    // Add pagination parameters
    if (paginate) {
      replacements.limit = limit;
      replacements.offset = (page - 1) * limit;
    }

    // Execute queries
    const [countResult] = await this.db.sequelize.query(countQuery, {
      replacements,
      type: this.db.Sequelize.QueryTypes.SELECT,
    });

    const rows = await this.db.sequelize.query(dataQuery, {
      replacements,
      type: this.db.Sequelize.QueryTypes.SELECT,
    });

    // Transform the flat results to match the expected structure
    const transformedRows = rows.map(row => ({
      id: row.id,
      itemCd: row.itemCd,
      itmDes: row.itmDes,
      unit: row.unit,
      acctCd: row.acctCd,
      gfq: row.gfq,
      remainingGfq: row.remainingGfq,
      isSteelbars: row.isSteelbars,
      tradeCode: row.tradeCode,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      trade: {
        code: row['trade.code'],
        tradeName: row['trade.tradeName']
      },
      steelbars: row['steelbars.id'] ? {
        id: row['steelbars.id'],
        weight: row['steelbars.weight'],
        ofmAcctcd: row['steelbars.ofmAcctcd']
      } : null,
    }));

    return {
      data: transformedRows,
      total: parseInt(countResult.count),
      totalPages: paginate ? Math.ceil(parseInt(countResult.count) / limit) : 1,
      currentPage: paginate ? page : 1,
    };
  }

  #mapFieldToColumn(field) {
    const mapping = {
      itmDes: 'i.itm_des',
      itemCd: 'i.item_cd',
      acctCd: 'i.acct_cd',
      unit: 'i.unit',
      remainingGfq: 'i.remaining_gfq',
      isSteelbars: 'i.is_steelbars',
      tradeCode: 'i.trade_code',
      createdAt: 'i.created_at',
      updatedAt: 'i.updated_at',
    };
    return mapping[field] || `i.${field}`;
  }

  async getItemDetails(id) {
    try {
      const item = await this.tableName.findByPk(id, {
        include: [
          {
            model: this.tableName.sequelize.model('trades'),
            as: 'trade',
            attributes: [
              ['trade_code', 'code'],
              ['trade_name', 'name'],
            ],
            required: false,
          },
          {
            model: this.tableName.sequelize.model('steelbars'),
            as: 'steelbars',
            required: false,
          },
        ],
        raw: true,
        nest: true,
      });

      if (!item) {
        throw this.clientErrors.NOT_FOUND({
          message: `Item with id of ${id} not found`,
        });
      }

      return {
        ...item,
        trade: item.trade?.code ? item.trade : null,
        steelbars: item.steelbars || null,
      };
    } catch (error) {
      console.error('Error in getItemDetails:', error);
      throw error;
    }
  }

  async getAllOfmItemLists(payload) {
    let whereClause = {};
    const { search, limit, page, order = [['listName', 'ASC']] } = payload;

    if (search) {
      whereClause.listName = { [this.Sequelize.Op.iLike]: `%${search}%` };
    }

    return await this.findAll({
      limit,
      page,
      order,
      where: whereClause,
      include: [{ model: this.db.projectModel }, { model: this.db.tradeModel }],
    });
  }

  async updateItem(item) {
    return await this.update(
      { id: item.id },
      { remainingGfq: item.gfq_balance },
    );
  }

  async getAllHistory(request) {
    const { id } = request.params;
    const {
      search,
      limit,
      page,
      paginate,
      sortBy = [['createdAt', 'DESC']],
    } = request.query;

    let whereClause = {};

    if (search) {
      whereClause = {
        rsNumber: { [this.Sequelize.Op.iLike]: `%${search}%` },
      };
    } else {
      whereClause = {
        itemId: id,
      };
    }

    const result = await this.historyRepository.findAll({
      limit,
      page,
      order: sortBy,
      paginate,
      where: whereClause,
    });

    return result;
  }

  async upsertItem(payload) {
    this.fastify.log.info(`Creating/Updating an Item...`);
    let createdItem;

    const existingRecord = await this.tableName.findOne({
      where: { itemCd: payload.acctCd },
    });

    if (existingRecord) {
      payload.unit = existingRecord.unit;
    }

    const [record] = await this.tableName.upsert(
      { ...payload, itemCd: payload.acctCd },
      {
        fields: [
          'itemCd',
          'itmDes',
          'unit',
          'acctCd',
          'gfq',
          'remainingGfq',
          'tradeCode',
        ],
      },
    );

    if (
      record.dataValues.createdAt.getTime() ===
      record.dataValues.updatedAt.getTime()
    ) {
      createdItem = record.dataValues;
    }

    this.fastify.log.info(`Successfully Created/Updated an Item`);

    return { ...record.dataValues, created: createdItem };
  }

  async getUniqueUnits() {
    return await this.findAll({
      where: {
        unit: {
          [this.db.Sequelize.Op.and]: [
            { [this.db.Sequelize.Op.ne]: '' },
            { [this.db.Sequelize.Op.not]: null },
          ],
        },
      },
      attributes: [
        [
          this.db.Sequelize.fn('DISTINCT', this.db.Sequelize.col('unit')),
          'unit',
        ],
      ],
      order: [['unit', 'ASC']],
      paginate: false,
    });
  }

  async bulkUpdateOfmItems(itemsArray) {
    this.fastify.log.info(`Bulk updating ${itemsArray.length} items...`);

    const results = [];
    const transaction = await this.db.sequelize.transaction();

    try {
      for (const item of itemsArray) {
        const { id, ...updateData } = item;

        const updateResult = await this.update({ id }, updateData, {
          transaction,
        });

        const updatedItem = await this.tableName.findByPk(id, {
          transaction,
          raw: true,
        });

        results.push(updatedItem);
      }

      await transaction.commit();
      this.fastify.log.info(
        `Successfully bulk updated ${results.length} items`,
      );
      return results;
    } catch (error) {
      await transaction.rollback();
      this.fastify.log.error(`Error in bulkUpdateOfmItems: ${error.message}`);
      throw error;
    }
  }

  async getItemPurchaseHistory(params) {
    const { id, order, page, limit, type } = params;

    // Default sorting: date purchased (latest first)
    const orderClauses =
      order && order.length > 0 ? order : [['datePurchased', 'DESC']];

    try {
      const parsedPage = parseInt(page) || 1;
      const parsedLimit = parseInt(limit) || 10;
      const offset = (parsedPage - 1) * parsedLimit;

      // Build the ORDER BY clause
      let orderByClause = '';
      const orderMappings = {
        rsNumber: 'r.rs_number',
        supplierName: 'supplier_name',
        pricePerUnit: 'unit_price',
        quantityPurchased: 'poi.quantity_purchased',
        datePurchased: 'po.created_at',
      };

      // Map the order clauses to SQL
      const orderSql = orderClauses
        .map(([field, direction]) => {
          const sqlField = orderMappings[field] || field;
          return `${sqlField} ${direction}`;
        })
        .join(', ');

      // Add secondary sort by date if not already included
      const hasDateSort = orderClauses.some(
        ([field]) => field === 'datePurchased',
      );
      const finalOrderSql = hasDateSort
        ? orderSql
        : `${orderSql}, po.created_at DESC`;

      orderByClause = `ORDER BY ${finalOrderSql}`;

      // Build the WHERE clause based on item type
      let whereClause = '';
      const replacements = { itemId: id };

      if (type === 'ofm') {
        whereClause =
          "AND ril.item_id = :itemId AND ril.item_type IN ('ofm', 'ofm-tom')";
      } else if (type === 'non-ofm') {
        whereClause =
          "AND ril.item_id = :itemId AND ril.item_type IN ('non-ofm', 'non-ofm-tom')";
      }

      // Add status filter for purchase orders
      const { PO_STATUS } = this.constants.purchaseOrder;
      whereClause += ` AND po.status IN ('${PO_STATUS.FOR_SENDING}', '${PO_STATUS.FOR_DELIVERY}', '${PO_STATUS.CLOSED_PO}')`;

      // Build the complete SQL query
      const countQuery = `
        SELECT COUNT(DISTINCT poi.id) as total
        FROM purchase_order_items poi
        JOIN purchase_orders po ON poi.purchase_order_id = po.id
        JOIN requisition_item_lists ril ON poi.requisition_item_list_id = ril.id
        JOIN requisitions r ON po.requisition_id = r.id
        LEFT JOIN suppliers s ON po.supplier_id = s.id AND po.supplier_type = 'supplier'
        LEFT JOIN projects p ON po.supplier_id = p.id AND po.supplier_type = 'project'
        LEFT JOIN companies c ON po.supplier_id = c.id AND po.supplier_type = 'company'
        LEFT JOIN canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
        WHERE 1=1 ${whereClause}
      `;

      const dataQuery = `
        SELECT 
          poi.id,
          poi.quantity_purchased,
          po.id as purchase_order_id,
          po.created_at as date_purchased,
          r.id as requisition_id,
          r.rs_number,
          r.rs_letter,
          r.company_code,
          s.id as supplier_id,
          COALESCE(s.name, p.name, c.name) as supplier_name,
          COALESCE(cis.unit_price, 0) as unit_price,
          cis.discount_value,
          cis.discount_type
        FROM purchase_order_items poi
        JOIN purchase_orders po ON poi.purchase_order_id = po.id
        JOIN requisition_item_lists ril ON poi.requisition_item_list_id = ril.id
        JOIN requisitions r ON po.requisition_id = r.id
        LEFT JOIN suppliers s ON po.supplier_id = s.id AND po.supplier_type = 'supplier'
        LEFT JOIN projects p ON po.supplier_id = p.id AND po.supplier_type = 'project'
        LEFT JOIN companies c ON po.supplier_id = c.id AND po.supplier_type = 'company'
        LEFT JOIN canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
        WHERE 1=1 ${whereClause}
        ${orderByClause}
        LIMIT :limit OFFSET :offset
      `;

      // Add pagination parameters
      replacements.limit = parsedLimit;
      replacements.offset = offset;

      // Execute the count query
      const [countResult] = await this.db.sequelize.query(countQuery, {
        replacements,
        type: this.db.Sequelize.QueryTypes.SELECT,
        raw: true,
      });

      const count = parseInt(countResult.total);

      // Execute the data query
      const rows = await this.db.sequelize.query(dataQuery, {
        replacements,
        type: this.db.Sequelize.QueryTypes.SELECT,
        raw: true,
      });

      // Format the response
      const formattedData = rows.map((row) => {
        let pricePerUnit = parseFloat(row.unit_price) || 0;

        // Apply discount based on discount type
        if (row.discount_value) {
          if (row.discount_type === 'percent') {
            pricePerUnit = pricePerUnit * (1 - row.discount_value / 100);
          } else if (row.discount_type === 'fixed') {
            pricePerUnit = pricePerUnit - row.discount_value;
          }
        }

        return {
          id: row.id,
          requisitionId: row.requisition_id,
          rsNumber: `RS-${row.company_code}${row.rs_letter}${row.rs_number}`,
          supplierName: row.supplier_name,
          pricePerUnit: parseFloat(pricePerUnit.toFixed(2)),
          quantityPurchased: row.quantity_purchased,
          datePurchased: row.date_purchased,
        };
      });

      return {
        rows: formattedData,
        count: count,
        page: parsedPage,
        limit: parsedLimit,
        totalPages: Math.ceil(count / parsedLimit),
      };
    } catch (error) {
      console.error('Error in getItemPurchaseHistory:', error);
      throw error;
    }
  }
}

module.exports = ItemRepository;
