const { generateNumber } = require('../../app/utils');
const {
  DELIVERY_ITEM_STATUSES,
} = require('../../domain/constants/deliveryReceiptItemConstants');
const BaseRepository = require('./baseRepository');

class DeliveryReceiptRepository extends BaseRepository {
  constructor({
    db,
    clientErrors,
    requisitionRepository,
    itemRepository,
    mockPurchaseOrderRepository,
  }) {
    super(db.deliveryReceiptModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.requisitionRepository = requisitionRepository;
    this.itemRepository = itemRepository;
    this.mockPurchaseOrderRepository = mockPurchaseOrderRepository;
  }

  /**
   * Create a delivery receipt.
   * @param {object} data - The data to use to create the delivery receipt.
   * @param {object} [options] - An object that contains optional parameters.
   * @param {Sequelize.Transaction} [options.transaction] - The transaction to use.
   * @returns {Promise<object>} The created delivery receipt as a JSON object.
   * @throws {ClientErrors.NotFound} If the requisition with the given id does not exist.
   * @throws {ClientErrors.BadRequest} If the requisition with the given id has no company code.
   */
  async create(data, { transaction, userId }) {
    const numberType = data.isDraft === 'true' ? 'draftDrNumber' : 'drNumber';
    const requisitionData = await this.requisitionRepository.getById(
      data.requisitionId,
    );

    if (!requisitionData) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${data.requisitionId} not found.`,
      });
    }

    if (!requisitionData.companyCode) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Requisition with id of ${data.requisitionId} has no company code.`,
      });
    }

    // generate delivery receipt number
    data.companyCode = requisitionData.companyCode;
    data[numberType] = await this.generateDRNumber(
      requisitionData.companyCode,
      data.isDraft,
      transaction,
    );

    const deliveryReceipt = await this.tableName.create(data, {
      include: {
        model: this.db.deliveryReceiptItemModel,
        as: 'items',
      },
      transaction: Object.assign(transaction, { userId }), // Attach userId to transaction
      userId,
      payload: data,
    });

    return deliveryReceipt.toJSON();
  }

  async getDeliveryReceiptById(payload = {}) {
    const { id, type } = payload;
    const isOfm = ['ofm', 'ofm-tom'].includes(type);

    const poItemInclude = [
      {
        model: this.db.purchaseOrderItemModel,
        as: 'purchaseOrderItem',
        required: true,
        attributes: [
          [
            this.Sequelize.literal(
              'CASE ' +
                'WHEN "purchaseOrderItem->canvassItemSupplier"."discount_type" = \'fixed\' THEN ' +
                '"purchaseOrderItem->canvassItemSupplier"."unit_price" - "purchaseOrderItem->canvassItemSupplier"."discount_value" ' +
                'WHEN "purchaseOrderItem->canvassItemSupplier"."discount_type" = \'percent\' THEN ' +
                '("purchaseOrderItem->canvassItemSupplier"."unit_price" * (1 - "purchaseOrderItem->canvassItemSupplier"."discount_value" / 100)) ' +
                'ELSE "purchaseOrderItem->canvassItemSupplier"."unit_price" - "purchaseOrderItem->canvassItemSupplier"."discount_value" ' +
                'END',
            ),
            'approvedPrice',
          ],
        ],
        include: {
          model: this.db.canvassItemSupplierModel,
          as: 'canvassItemSupplier',
          attributes: [],
        },
      },
    ];

    const itemInclude = isOfm
      ? [
          {
            model: this.db.itemModel,
            as: 'item',
            attributes: ['acctCd'],
          },
        ]
      : [];

    const deliveryReceipt = await this.tableName.findOne({
      where: { id },
      include: [
        {
          model: this.db.deliveryReceiptItemModel,
          as: 'items',
          separate: true,
          order: [['id', 'ASC']],
          include: [...itemInclude, ...poItemInclude],
        },
        {
          model: this.db.purchaseOrderModel,
          attributes: [
            'id',
            [
              this.Sequelize.fn(
                'CONCAT',
                'PO-',
                this.Sequelize.col('purchaseOrder->requisition.company_code'),
                this.Sequelize.col('purchaseOrder.po_letter'),
                this.Sequelize.col('purchaseOrder.po_number'),
              ),
              'poNumber',
            ],
          ],
          as: 'purchaseOrder',
          include: [
            {
              model: this.db.requisitionModel,
              as: 'requisition',
              attributes: [],
            },
          ],
        },
        {
          model: this.db.invoiceReportModel,
          attributes: ['id', 'irNumber'],
          as: 'invoiceReport',
        },
        {
          model: this.db.attachmentModel,
          as: 'attachments',
          where: { model: ['delivery_receipt'] },
          required: false,
        },
      ],
    });

    return deliveryReceipt;
  }

  async update(deliveryReceipt, data, { transaction, userId }) {
    const [updateCount, updatedRecords] = await this.tableName.update(data, {
      where: { id: deliveryReceipt.id },
      include: {
        model: this.db.deliveryReceiptItemModel,
        as: 'items',
      },
      transaction,
      userId,
      returning: true,
    });

    if (updateCount === 0) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Delivery receipt not found',
      });
    }

    return updatedRecords[0].get();
  }

  async generateDRNumber(companyCode, isDraft, transaction = null) {
    const numberType = isDraft === 'true' ? 'draftDrNumber' : 'drNumber';
    const lastDeliveryReceipt = await this.tableName.findOne({
      where: {
        companyCode,
        [numberType]: {
          [this.Sequelize.Op.ne]: null,
        },
      },
      order: [[numberType, 'DESC']],
      transaction,
      lock: true,
    });

    if (lastDeliveryReceipt) {
      return generateNumber(companyCode, lastDeliveryReceipt[numberType]);
    } else {
      return generateNumber(companyCode);
    }
  }

  async getDeliveryReceiptsFromRequisitionId(
    requisitionId,
    { search, page, limit, sortBy },
  ) {
    const formattedOrder = [];
    const sortByObject = sortBy
      ? JSON.parse(sortBy)
      : { latestDeliveryDate: 'DESC' };
    for (const [field, direction] of Object.entries(sortByObject)) {
      formattedOrder.push([field, direction]);
    }

    const deliveryReceipts = await this.findAll({
      attributes: [
        'id',
        [
          this.Sequelize.fn(
            'CONCAT',
            'RR-',
            this.Sequelize.literal(
              "CASE WHEN \"delivery_receipts\".\"is_draft\" = 'true' THEN 'TMP-' ELSE '' END",
            ),
            this.Sequelize.fn(
              'COALESCE',
              this.Sequelize.col('dr_number'),
              this.Sequelize.col('draft_dr_number'),
            ),
          ),
          'drNumber',
        ],
        'supplier',
        'latestDeliveryDate',
        'latestDeliveryStatus',
        'isDraft',
      ],
      where: {
        [this.db.Sequelize.Op.and]: [
          search
            ? this.db.Sequelize.where(
                this.db.Sequelize.literal(
                  `CONCAT('RR-', CASE WHEN "delivery_receipts"."is_draft" = 'true' THEN 'TMP-' ELSE '' END, COALESCE("dr_number", "draft_dr_number"))`,
                ),
                { [this.db.Sequelize.Op.iLike]: `%${search}%` },
              )
            : null,
          { requisitionId },
        ],
      },
      paginate: true,
      page,
      limit,
      order: formattedOrder,
    });

    return deliveryReceipts;
  }

  async getDeliveryReturnsFromRequisitionId(
    requisitionId,
    { search, page, limit, sortBy },
  ) {
    const formattedOrder = [];
    const sortByObject = sortBy
      ? JSON.parse(sortBy)
      : { latestDeliveryDate: 'DESC' };
    for (const [field, direction] of Object.entries(sortByObject)) {
      formattedOrder.push([field, direction]);
    }

    const deliveryReturns = await this.findAll({
      attributes: [
        'id',
        [
          this.Sequelize.fn(
            'CONCAT',
            'RR-',
            this.Sequelize.literal(
              "CASE WHEN \"delivery_receipts\".\"is_draft\" = 'true' THEN 'TMP-' ELSE '' END",
            ),
            this.Sequelize.fn(
              'COALESCE',
              this.Sequelize.col('dr_number'),
              this.Sequelize.col('draft_dr_number'),
            ),
          ),
          'drNumber',
        ],
        'supplier',
        'latestDeliveryDate',
        'latestDeliveryStatus',
        'isDraft',
      ],
      where: {
        [this.db.Sequelize.Op.and]: [
          search
            ? this.db.Sequelize.where(
                this.db.Sequelize.literal(
                  `CONCAT('RR-', CASE WHEN "delivery_receipts"."is_draft" = 'true' THEN 'TMP-' ELSE '' END, COALESCE("dr_number", "draft_dr_number"))`,
                ),
                { [this.db.Sequelize.Op.iLike]: `%${search}%` },
              )
            : null,
          { requisitionId },
          {
            latestDeliveryStatus: {
              [this.db.Sequelize.Op.in]: [
                DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS,
                DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS,
              ],
            },
          },
        ],
      },
      include: [
        {
          association: 'items',
          attributes: [
            'id',
            'drId',
            'itemDes',
            'dateDelivered',
            'updatedAt',
            'qtyOrdered',
            'qtyDelivered',
            'qtyReturned',
          ],
          required: true,
        },
        {
          association: 'purchaseOrder',
          attributes: ['id'],
          include: [
            {
              association: 'purchaseOrderApprovers',
              attributes: ['id', 'status', 'updatedAt', 'userId'],
              include: [
                {
                  association: 'approver',
                  attributes: ['id', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        },
      ],
      paginate: true,
      page,
      limit,
      order: formattedOrder,
    });

    return deliveryReturns;
  }

  async syncWithInvoiceReport(deliveryReceiptIds, invoiceId, options = {}) {
    const currentDeliveryReceiptIds = await this.findAll({
      attributes: ['id'],
      where: {
        invoiceId,
      },
    });

    const newDeliveryReceiptIds = deliveryReceiptIds.filter(
      (id) => !currentDeliveryReceiptIds.data.map((dr) => dr.id).includes(id),
    );

    const removedDeliveryReceiptIds = currentDeliveryReceiptIds.data
      .map((dr) => dr.id)
      .filter((id) => !deliveryReceiptIds.includes(id));

    if (newDeliveryReceiptIds.length > 0) {
      await this.update(
        {
          id: {
            [this.db.Sequelize.Op.in]: newDeliveryReceiptIds,
          },
        },
        { invoiceId },
        options,
      );
    }

    if (removedDeliveryReceiptIds.length > 0) {
      await this.update(
        {
          id: {
            [this.db.Sequelize.Op.in]: removedDeliveryReceiptIds,
          },
        },
        { invoiceId: null },
        options,
      );
    }
  }
}

module.exports = DeliveryReceiptRepository;
